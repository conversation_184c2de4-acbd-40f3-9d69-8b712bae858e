#!/bin/bash

# 材质编译脚本

set -e

echo "🔨 编译 Filament 材质..."

# 检查 matc 是否可用
MATC_PATH="Pods/Filament/bin/matc"

if [ ! -f "$MATC_PATH" ]; then
    echo "❌ matc 编译器未找到，尝试从下载的包中查找..."
    
    # 查找可能的 matc 位置
    if [ -f "Pods/Filament/out/release/filament/bin/matc" ]; then
        MATC_PATH="Pods/Filament/out/release/filament/bin/matc"
    elif [ -f "Pods/Filament/tools/matc" ]; then
        MATC_PATH="Pods/Filament/tools/matc"
    else
        echo "❌ 无法找到 matc 编译器"
        echo "📝 将使用内嵌的简单材质数据"
        exit 0
    fi
fi

# 编译材质
echo "使用 matc: $MATC_PATH"

# 创建输出目录
mkdir -p iOS3DRenderUI/Materials

# 编译简单材质
if [ -f "iOS3DRenderUI/SimpleMaterial.mat" ]; then
    echo "编译 SimpleMaterial.mat..."
    "$MATC_PATH" -p mobile -o iOS3DRenderUI/Materials/SimpleMaterial.filamat iOS3DRenderUI/SimpleMaterial.mat
    echo "✅ 材质编译完成"
else
    echo "❌ 材质源文件未找到"
    exit 1
fi