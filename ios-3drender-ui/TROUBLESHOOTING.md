# 故障排除指南

本文档列出了在构建和运行 iOS 3D 纹理渲染器时可能遇到的常见问题及解决方案。

## 构建问题

### 1. CocoaPods 兼容性错误

**错误信息**：
```
ArgumentError - [Xcodeproj] Unable to find compatibility version string for object version `70`.
```

**原因**：Xcode 16.4+ 与当前版本的 CocoaPods 存在兼容性问题。

**解决方案**：

#### 方案一：更新 CocoaPods
```bash
sudo gem update cocoapods
pod repo update
```

#### 方案二：使用 Swift Package Manager
1. 打开 `iOS3DRenderUI.xcodeproj`
2. File -> Add Package Dependencies
3. 输入：`https://github.com/google/filament`
4. 选择合适的版本并添加

#### 方案三：下载预编译框架
1. 访问：https://github.com/google/filament/releases
2. 下载适合 iOS 的预编译框架
3. 手动添加到 Xcode 项目中

### 2. XcodeGen 项目生成失败

**错误信息**：
```
Unknown Target dependency: ["pod": "Filament"]
```

**解决方案**：
这个错误已在当前版本修复。如果仍遇到，请使用手动创建项目的方法。

### 3. 缺少开发团队 ID

**错误信息**：
```
Code signing is required for product type 'Application'
```

**解决方案**：
1. 在 Xcode 中选择项目
2. 选择 Target -> Signing & Capabilities
3. 选择你的开发团队
4. 或在 `project.yml` 中填入你的 Team ID

## 运行时问题

### 1. 应用崩溃 - Metal 不可用

**错误信息**：
```
Metal is not available on this device
```

**解决方案**：
- 确保在支持 Metal 的设备上运行（iOS 8.0+ 的真实设备）
- 模拟器可能不支持所有 Metal 特性

### 2. 模型加载失败

**可能原因**：
- 模型文件格式不支持
- 文件损坏或不完整
- 内存不足

**解决方案**：
1. 确保使用 glTF 2.0 或 GLB 格式
2. 检查模型文件大小（建议小于 50MB）
3. 尝试使用简单的测试模型

### 3. 纹理不显示

**可能原因**：
- 图片格式不支持
- 模型缺少 UV 坐标
- 材质应用失败

**解决方案**：
1. 使用标准图片格式（PNG, JPEG）
2. 确保 3D 模型包含正确的 UV 映射
3. 检查控制台日志获取详细错误信息

### 4. 性能问题

**症状**：
- 帧率低下
- 界面卡顿
- 设备发热

**解决方案**：
1. 减少模型复杂度
2. 优化纹理分辨率
3. 监控内存使用
4. 使用 Instruments 进行性能分析

## 开发问题

### 1. 找不到 Filament 模块

**错误信息**：
```
No such module 'Filament'
```

**解决方案**：
1. 确保正确安装了 Filament 依赖
2. 清理并重新构建项目 (⌘+Shift+K)
3. 检查 Build Settings 中的 Framework Search Paths

### 2. Swift 编译错误

**常见错误**：
- 类型不匹配
- 方法签名错误

**解决方案**：
1. 检查 Filament Swift API 文档
2. 确保使用正确的 Filament 版本
3. 查看示例代码中的正确用法

### 3. 内存泄漏

**症状**：
- 内存使用持续增长
- 应用最终崩溃

**解决方案**：
1. 正确释放 Filament 对象
2. 使用 weak 引用避免循环引用
3. 在 deinit 中清理资源

## 调试技巧

### 1. 启用详细日志

在 `FilamentRenderer` 中添加日志：

```swift
// 在关键操作前后添加日志
print("Loading model...")
// ... 模型加载代码
print("Model loaded successfully")
```

### 2. 使用 Metal Debugger

1. 在 Xcode 中运行应用
2. 点击 Metal 按钮启用 GPU 调试
3. 查看渲染管线和资源使用情况

### 3. 内存监控

1. 使用 Instruments 的 Allocations 工具
2. 监控 Filament 相关对象的生命周期
3. 检查是否有内存泄漏

### 4. 分步测试

1. 先测试基础 Filament 初始化
2. 逐步添加模型加载
3. 最后添加纹理功能

## 获取帮助

如果以上解决方案都无法解决问题：

1. **检查 Filament 官方文档**：
   - https://google.github.io/filament/

2. **查看 Filament GitHub Issues**：
   - https://github.com/google/filament/issues

3. **提交新 Issue**：
   - 提供详细的错误信息
   - 包含重现步骤
   - 说明设备和系统版本

4. **联系开发者**：
   - 在项目 Issue 中描述问题
   - 提供完整的错误日志

## 版本兼容性

| Xcode 版本 | CocoaPods 状态 | 推荐方案 |
|-----------|---------------|----------|
| 15.x      | ✅ 正常工作    | CocoaPods |
| 16.0-16.3 | ⚠️ 部分问题    | 更新 CocoaPods |
| 16.4+     | ❌ 已知问题    | Swift Package Manager |

根据你的开发环境选择合适的集成方案。