name: iOS3DRenderUI
options:
  bundleIdPrefix: com.example.ios3drender
  createIntermediateGroups: true
  developmentLanguage: en
  xcodeVersion: "14.3"  # 使用兼容的 Xcode 版本
  minimumXcodeGenVersion: "2.32.0"

settings:
  IPHONEOS_DEPLOYMENT_TARGET: 14.0
  SWIFT_VERSION: 5.0
  # 强制使用兼容的项目格式
  XCODE_COMPATIBILITY_VERSION: "Xcode 14.0"

targets:
  iOS3DRenderUI:
    type: application
    platform: iOS
    deploymentTarget: "14.0"
    sources:
      - path: iOS3DRenderUI
        excludes:
          - "*.plist"
    settings:
      PRODUCT_BUNDLE_IDENTIFIER: com.example.ios3drender.iOS3DRenderUI
      DEVELOPMENT_TEAM: # 需要填入你的开发团队 ID
      CODE_SIGN_STYLE: Automatic
      INFOPLIST_FILE: iOS3DRenderUI/Info.plist
      ASSETCATALOG_COMPILER_APPICON_NAME: AppIcon
      ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME: AccentColor
      ENABLE_BITCODE: NO
      SWIFT_OBJC_BRIDGING_HEADER: iOS3DRenderUI/iOS3DRenderUI-Bridging-Header.h
      # C++ 配置
      CLANG_CXX_LANGUAGE_STANDARD: c++17
      CLANG_CXX_LIBRARY: libc++
      CLANG_ENABLE_MODULES: YES
      CLANG_ENABLE_OBJC_ARC: YES
      GCC_C_LANGUAGE_STANDARD: c11
      # 头文件搜索路径
      HEADER_SEARCH_PATHS: 
        - "$(PODS_ROOT)/Filament/include"
        - "$(PODS_ROOT)/Headers/Public"
        - "$(PODS_ROOT)/Headers/Public/Filament"
      # 库搜索路径
      LIBRARY_SEARCH_PATHS:
        - "$(PODS_ROOT)/Filament/lib/ios"
        - "$(inherited)"
      # 其他链接器设置
      OTHER_LDFLAGS:
        - "-ObjC"
        - "$(inherited)"
    scheme:
      testTargets:
        - iOS3DRenderUITests
      gatherCoverageData: false
      
  iOS3DRenderUITests:
    type: bundle.unit-test
    platform: iOS
    sources:
      - iOS3DRenderUITests
    dependencies:
      - target: iOS3DRenderUI