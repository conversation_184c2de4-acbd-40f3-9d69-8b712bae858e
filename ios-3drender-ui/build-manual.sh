#!/bin/bash

# iOS 3D 渲染器手动构建脚本（解决 CocoaPods 兼容性问题）

set -e

echo "🚀 开始手动构建 iOS 3D 渲染器项目..."

# 检查是否存在 Xcode 项目
if [ ! -f "iOS3DRenderUI.xcodeproj/project.pbxproj" ]; then
    echo "❌ 请先手动创建 Xcode 项目："
    echo "   1. 打开 Xcode"
    echo "   2. 创建新项目 -> iOS -> App"
    echo "   3. 项目名：iOS3DRenderUI"
    echo "   4. 语言：Swift，最低部署目标：iOS 14.0"
    echo "   5. 保存到当前目录"
    echo "   6. 将 iOS3DRenderUI/ 文件夹中的源码文件添加到项目"
    echo ""
    echo "创建完成后重新运行此脚本"
    exit 1
fi

echo "✅ 发现 Xcode 项目文件"

# 更新 CocoaPods
echo "📚 更新 CocoaPods..."
sudo gem update cocoapods --no-document

# 尝试安装依赖
echo "📦 安装 CocoaPods 依赖..."
if pod install; then
    echo "✅ CocoaPods 安装成功！"
else
    echo "❌ CocoaPods 安装失败，尝试替代方案..."
    
    # 检查是否有备用方案 - Swift Package Manager
    echo "🔄 尝试使用 Swift Package Manager..."
    echo "请手动在 Xcode 中添加 Filament 依赖："
    echo "   1. 打开 iOS3DRenderUI.xcodeproj"
    echo "   2. 选择项目根节点 -> Package Dependencies"
    echo "   3. 点击 + 添加包"
    echo "   4. 输入：https://github.com/google/filament"
    echo "   5. 选择合适的版本并添加"
    echo ""
    echo "或者下载预编译的 Filament 框架："
    echo "   https://github.com/google/filament/releases"
fi

echo "✅ 构建完成！"
echo "📱 请打开 iOS3DRenderUI.xcodeproj 或 iOS3DRenderUI.xcworkspace（如果 CocoaPods 成功）"

# 可选：自动打开项目
read -p "是否要自动打开 Xcode 项目？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -f "iOS3DRenderUI.xcworkspace" ]; then
        open iOS3DRenderUI.xcworkspace
    else
        open iOS3DRenderUI.xcodeproj
    fi
fi