//
//  FilamentMaterialManager.mm
//  iOS3DRenderUI
//
//  专门处理纹理材质的管理器实现
//

#import "FilamentMaterialManager.h"

// 包含 Filament C++ 头文件
#include <filament/Engine.h>
#include <filament/Material.h>
#include <filament/MaterialInstance.h>
#include <filament/Texture.h>
#include <filament/TextureSampler.h>
#include <backend/PixelBufferDescriptor.h>
#include <math/vec3.h>
#include <math/vec2.h>

using namespace filament;

@interface FilamentMaterialManager () {
    Engine* _engine;
    Material* _texturedMaterial;
    MaterialInstance* _currentMaterialInstance;
}
@end

@implementation FilamentMaterialManager

- (instancetype)initWithEngine:(void*)engine {
    self = [super init];
    if (self) {
        _engine = (Engine*)engine;
        _texturedMaterial = nullptr;
        _currentMaterialInstance = nullptr;
    }
    return self;
}

- (void)dealloc {
    if (_engine) {
        if (_currentMaterialInstance) {
            _engine->destroy(_currentMaterialInstance);
        }
        if (_texturedMaterial) {
            _engine->destroy(_texturedMaterial);
        }
    }
}

#pragma mark - 材质创建和管理

- (void*)createTexturedMaterial {
    NSLog(@"🔧 开始创建纹理支持材质...");

    // 方案1: 尝试加载预编译材质
    if ([self loadPrecompiledMaterial:@"textured_pbr"]) {
        NSLog(@"✅ 成功加载预编译纹理材质");
        return _currentMaterialInstance;
    }

    // 方案2: 创建运行时材质
    NSLog(@"⚠️ 预编译材质不可用，尝试创建运行时材质...");
    MaterialInstance* runtimeMaterial = [self createRuntimeTexturedMaterial];
    if (runtimeMaterial) {
        _currentMaterialInstance = runtimeMaterial;
        NSLog(@"✅ 成功创建运行时纹理材质");
        return _currentMaterialInstance;
    }

    // 方案3: 降级到默认材质
    NSLog(@"⚠️ 降级到默认材质");
    return [self getFallbackMaterial];
}

- (BOOL)loadPrecompiledMaterial:(NSString*)materialName {
    // 查找预编译的材质文件
    NSString *materialPath = [[NSBundle mainBundle] pathForResource:materialName
                                                             ofType:@"filamat"
                                                        inDirectory:@"Materials"];
    if (!materialPath) {
        NSLog(@"⚠️ 未找到预编译材质文件: %@.filamat", materialName);
        return NO;
    }

    NSData *materialData = [NSData dataWithContentsOfFile:materialPath];
    if (!materialData || materialData.length == 0) {
        NSLog(@"❌ 无法读取材质文件或文件为空");
        return NO;
    }

    try {
        // 从二进制数据创建材质
        _texturedMaterial = Material::Builder()
            .package(materialData.bytes, materialData.length)
            .build(*_engine);

        if (!_texturedMaterial) {
            NSLog(@"❌ 材质创建失败");
            return NO;
        }

        // 创建材质实例
        _currentMaterialInstance = _texturedMaterial->createInstance();
        if (!_currentMaterialInstance) {
            NSLog(@"❌ 材质实例创建失败");
            return NO;
        }

        NSLog(@"✅ 预编译材质加载成功: %s", _texturedMaterial->getName());

        // 验证材质支持的参数
        [self logMaterialParameters:_texturedMaterial];

        return YES;

    } catch (const std::exception& e) {
        NSLog(@"❌ 加载预编译材质时发生异常: %s", e.what());
        return NO;
    } catch (...) {
        NSLog(@"❌ 加载预编译材质时发生未知异常");
        return NO;
    }
}

- (void*)getFallbackMaterial {
    if (_currentMaterialInstance) {
        return _currentMaterialInstance;
    }

    // 创建默认材质实例
    _currentMaterialInstance = _engine->getDefaultMaterial()->createInstance();
    NSLog(@"⚠️ 使用默认材质作为降级方案");
    return _currentMaterialInstance;
}

#pragma mark - 运行时材质创建

- (MaterialInstance*)createRuntimeTexturedMaterial {
    NSLog(@"🛠️ 尝试创建运行时纹理材质...");

    try {
        // 创建基于源码的材质
        const char* materialSource = R"(
            material {
                name : RuntimeTextured,
                shadingModel : lit,
                blending : opaque,
                parameters : [
                    {
                        type : sampler2d,
                        name : baseColorMap
                    },
                    {
                        type : float3,
                        name : baseColorFactor,
                        default : [1.0, 1.0, 1.0]
                    },
                    {
                        type : float,
                        name : metallicFactor,
                        default : 0.0
                    },
                    {
                        type : float,
                        name : roughnessFactor,
                        default : 0.5
                    }
                ]
            }

            fragment {
                void material(inout MaterialInputs material) {
                    prepareMaterial(material);
                    float4 baseColor = texture(materialParams_baseColorMap, getUV0());
                    material.baseColor = baseColor * float4(materialParams.baseColorFactor, 1.0);
                    material.metallic = materialParams.metallicFactor;
                    material.roughness = materialParams.roughnessFactor;
                }
            }
        )";

        // 注意：这种方法需要材质编译器在运行时可用
        // 在实际的 iOS 应用中，这通常不可行
        NSLog(@"⚠️ 运行时材质编译在移动设备上不支持");
        return nullptr;

    } catch (...) {
        NSLog(@"❌ 运行时材质创建失败");
        return nullptr;
    }
}

#pragma mark - 纹理应用

- (BOOL)applyTexture:(void*)texture toMaterial:(void*)materialInstance {
    if (!texture || !materialInstance) {
        NSLog(@"❌ 纹理或材质实例为空");
        return NO;
    }

    Texture* filamentTexture = (Texture*)texture;
    MaterialInstance* matInstance = (MaterialInstance*)materialInstance;

    NSLog(@"🎨 开始应用纹理到材质...");

    try {
        // 创建纹理采样器
        TextureSampler sampler = TextureSampler(
            TextureSampler::MinFilter::LINEAR,
            TextureSampler::MagFilter::LINEAR,
            TextureSampler::WrapMode::REPEAT
        );

        // 尝试应用纹理到 baseColorMap 参数
        const Material* material = matInstance->getMaterial();

        if (material->hasParameter("baseColorMap")) {
            matInstance->setParameter("baseColorMap", filamentTexture, sampler);
            NSLog(@"✅ 成功设置 baseColorMap 纹理参数");

            // 设置基础颜色因子为白色，确保纹理完全显示
            if (material->hasParameter("baseColorFactor")) {
                math::float3 whiteColor = {1.0f, 1.0f, 1.0f};
                matInstance->setParameter("baseColorFactor", whiteColor);
                NSLog(@"✅ 设置 baseColorFactor 为白色");
            }

            return YES;
        } else {
            NSLog(@"❌ 材质不支持 baseColorMap 参数");
            return NO;
        }

    } catch (const std::exception& e) {
        NSLog(@"❌ 应用纹理时发生异常: %s", e.what());
        return NO;
    } catch (...) {
        NSLog(@"❌ 应用纹理时发生未知异常");
        return NO;
    }
}

- (BOOL)setTextureParameters:(void*)materialInstance
                     texture:(void*)texture
                      offset:(CGPoint)offset
                       scale:(float)scale {
    if (!materialInstance) {
        return NO;
    }

    MaterialInstance* matInstance = (MaterialInstance*)materialInstance;
    const Material* material = matInstance->getMaterial();

    try {
        // 设置纹理偏移
        if (material->hasParameter("textureOffset")) {
            math::float2 textureOffset = {(float)offset.x, (float)offset.y};
            matInstance->setParameter("textureOffset", textureOffset);
            NSLog(@"✅ 设置纹理偏移: (%.3f, %.3f)", offset.x, offset.y);
        }

        // 设置纹理缩放
        if (material->hasParameter("textureScale")) {
            matInstance->setParameter("textureScale", scale);
            NSLog(@"✅ 设置纹理缩放: %.3f", scale);
        }

        return YES;

    } catch (const std::exception& e) {
        NSLog(@"❌ 设置纹理参数时发生异常: %s", e.what());
        return NO;
    }
}

#pragma mark - 材质参数设置

- (BOOL)setMaterialColor:(void*)materialInstance color:(UIColor*)color {
    if (!materialInstance || !color) {
        return NO;
    }

    MaterialInstance* matInstance = (MaterialInstance*)materialInstance;
    const Material* material = matInstance->getMaterial();

    // 提取 UIColor 的 RGB 分量
    CGFloat red, green, blue, alpha;
    [color getRed:&red green:&green blue:&blue alpha:&alpha];

    math::float3 colorVec = {(float)red, (float)green, (float)blue};

    try {
        if (material->hasParameter("baseColorFactor")) {
            matInstance->setParameter("baseColorFactor", colorVec);
            NSLog(@"✅ 设置材质颜色: (%.3f, %.3f, %.3f)", red, green, blue);
            return YES;
        } else if (material->hasParameter("baseColor")) {
            matInstance->setParameter("baseColor", RgbType::sRGB, colorVec);
            NSLog(@"✅ 设置材质 baseColor: (%.3f, %.3f, %.3f)", red, green, blue);
            return YES;
        } else {
            NSLog(@"⚠️ 材质不支持颜色参数");
            return NO;
        }

    } catch (const std::exception& e) {
        NSLog(@"❌ 设置材质颜色时发生异常: %s", e.what());
        return NO;
    }
}

- (BOOL)setMaterialPBRParameters:(void*)materialInstance
                        metallic:(float)metallic
                       roughness:(float)roughness {
    if (!materialInstance) {
        return NO;
    }

    MaterialInstance* matInstance = (MaterialInstance*)materialInstance;
    const Material* material = matInstance->getMaterial();

    try {
        BOOL success = NO;

        if (material->hasParameter("metallicFactor")) {
            matInstance->setParameter("metallicFactor", metallic);
            NSLog(@"✅ 设置金属度: %.3f", metallic);
            success = YES;
        }

        if (material->hasParameter("roughnessFactor")) {
            matInstance->setParameter("roughnessFactor", roughness);
            NSLog(@"✅ 设置粗糙度: %.3f", roughness);
            success = YES;
        }

        return success;

    } catch (const std::exception& e) {
        NSLog(@"❌ 设置 PBR 参数时发生异常: %s", e.what());
        return NO;
    }
}

#pragma mark - 辅助方法

- (void)logMaterialParameters:(Material*)material {
    if (!material) return;

    NSLog(@"📋 材质参数信息:");
    NSLog(@"   材质名称: %s", material->getName());

    // 检查常见的纹理参数
    NSArray* textureParams = @[@"baseColorMap", @"diffuseMap", @"albedoMap", @"colorMap"];
    NSArray* colorParams = @[@"baseColorFactor", @"baseColor", @"color"];
    NSArray* pbrParams = @[@"metallicFactor", @"roughnessFactor", @"emissiveFactor"];
    NSArray* transformParams = @[@"textureOffset", @"textureScale"];

    NSLog(@"   纹理参数:");
    for (NSString* param in textureParams) {
        const char* cParam = [param UTF8String];
        if (material->hasParameter(cParam)) {
            NSLog(@"     ✅ %@", param);
        }
    }

    NSLog(@"   颜色参数:");
    for (NSString* param in colorParams) {
        const char* cParam = [param UTF8String];
        if (material->hasParameter(cParam)) {
            NSLog(@"     ✅ %@", param);
        }
    }

    NSLog(@"   PBR参数:");
    for (NSString* param in pbrParams) {
        const char* cParam = [param UTF8String];
        if (material->hasParameter(cParam)) {
            NSLog(@"     ✅ %@", param);
        }
    }

    NSLog(@"   变换参数:");
    for (NSString* param in transformParams) {
        const char* cParam = [param UTF8String];
        if (material->hasParameter(cParam)) {
            NSLog(@"     ✅ %@", param);
        }
    }
}

@end