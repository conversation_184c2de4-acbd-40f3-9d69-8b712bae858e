material {
    name : SimpleMaterial,
    
    parameters : [
        {
            type : sampler2d,
            name : baseColorMap
        },
        {
            type : float3,
            name : baseColor
        }
    ],
    
    requires : [
        uv0
    ],
    
    vertex {
        void materialVertex(inout MaterialVertexInputs material) {
            material.uv0 = getUV0();
        }
    },
    
    fragment {
        void material(inout MaterialInputs material) {
            prepareMaterial(material);
            material.baseColor = materialParams.baseColor;
            
            if (materialParams_baseColorMap != null) {
                vec4 texColor = texture(materialParams_baseColorMap, getUV0());
                material.baseColor.rgb *= texColor.rgb;
            }
        }
    }
}