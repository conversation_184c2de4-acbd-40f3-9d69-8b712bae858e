//
//  FilamentWrapper.h
//  iOS3DRenderUI
//
//  Objective-C++ 包装器，用于在 Swift 中使用 Filament C++ API
//

#import <Foundation/Foundation.h>
#import <MetalKit/MetalKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface FilamentWrapper : NSObject

// 初始化和清理
- (instancetype)init;
- (void)dealloc;

// 表面设置
- (BOOL)setupWithMetalLayer:(CAMetalLayer *)metalLayer;
- (void)resizeWithWidth:(NSUInteger)width height:(NSUInteger)height;

// 渲染
- (void)render;

// 模型加载
- (BOOL)loadModelFromData:(NSData *)data;
- (void)removeCurrentModel;
- (BOOL)createDefaultSphere; // 创建内置球体模型
- (void)setCameraDistance:(float)distance; // 调整相机距离

// 材质管理 (私有方法声明，用于内部调用)
// - (MaterialInstance*)loadCustomMaterial; // 私有方法，不在头文件中声明
// - (BOOL)updateSphereVertexColor:(math::float4)color; // 私有方法，不在头文件中声明

// 纹理管理
- (BOOL)loadTextureFromUIImage:(UIImage *)image;
- (void)clearTextures;
- (BOOL)selectTextureAtIndex:(NSInteger)index;
- (NSInteger)getTextureCount;

// 纹理变换
- (void)moveTextureWithDeltaX:(float)deltaX deltaY:(float)deltaY;
- (void)resetTexturePosition;

// 动画控制
- (void)startTextureAnimationWithSpeed:(float)speed;
- (void)stopTextureAnimation;

@end

NS_ASSUME_NONNULL_END