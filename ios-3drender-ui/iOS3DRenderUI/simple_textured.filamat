material {
    name : SimpleTexturedMaterial,
    
    parameters : [
        {
            type : sampler2d,
            name : baseColorMap
        },
        {
            type : float3,
            name : baseColor
        },
        {
            type : float,
            name : baseColorFactor
        }
    ],
    
    requires : [
        uv0
    ],
    
    shadingModel : unlit,
    
    blending : opaque,
    
    culling : none
}

fragment {
    void material(inout MaterialInputs material) {
        prepareMaterial(material);
        
        float2 uv = getUV0();
        float3 baseColor = materialParams.baseColor;
        float baseColorFactor = materialParams.baseColorFactor;
        
        // 采样纹理
        float4 texColor = texture(materialParams_baseColorMap, uv);
        
        // 混合纹理和基础颜色
        material.baseColor.rgb = baseColor * baseColorFactor * texColor.rgb;
        material.baseColor.a = texColor.a;
    }
}