material {
    name : SimpleMaterial,
    
    parameters : [
        {
            type : sampler2d,
            name : baseColorMap
        },
        {
            type : float4,
            name : baseColorFactor
        },
        {
            type : float,
            name : roughnessFactor
        },
        {
            type : float,
            name : metallicFactor
        }
    ],
    
    requires : [
        uv0
    ],
    
    shadingModel : lit,
    blending : opaque
}

fragment {
    void material(inout MaterialInputs material) {
        prepareMaterial(material);
        material.baseColor = texture(materialParams_baseColorMap, getUV0());
        material.baseColor *= materialParams.baseColorFactor;
        material.roughness = materialParams.roughnessFactor;
        material.metallic = materialParams.metallicFactor;
    }
}

vertex {
    void materialVertex(inout MaterialVertexInputs material) {
    }
}