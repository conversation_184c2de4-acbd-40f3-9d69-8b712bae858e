//
//  FilamentMaterialManager.h
//  iOS3DRenderUI
//
//  专门处理纹理材质的管理器
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

// 前向声明 C++ 类
namespace filament {
    class Engine;
    class Material;
    class MaterialInstance;
    class Texture;
}

NS_ASSUME_NONNULL_BEGIN

@interface FilamentMaterialManager : NSObject

- (instancetype)initWithEngine:(void*)engine;

// 材质创建和管理
- (void*)createTexturedMaterial;  // 返回 MaterialInstance*
- (BOOL)loadPrecompiledMaterial:(NSString*)materialName;
- (void*)getFallbackMaterial;

// 纹理应用
- (BOOL)applyTexture:(void*)texture toMaterial:(void*)materialInstance;
- (BOOL)setTextureParameters:(void*)materialInstance 
                     texture:(void*)texture
                      offset:(CGPoint)offset
                       scale:(float)scale;

// 材质参数设置
- (BOOL)setMaterialColor:(void*)materialInstance color:(UIColor*)color;
- (BOOL)setMaterialPBRParameters:(void*)materialInstance 
                        metallic:(float)metallic 
                       roughness:(float)roughness;

@end

NS_ASSUME_NONNULL_END