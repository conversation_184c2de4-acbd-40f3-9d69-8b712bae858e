#!/bin/bash

# Filament 材质编译脚本
# 注意：需要安装 Filament 工具链

echo "开始编译 Filament 材质..."

# 检查是否有 matc 编译器
if ! command -v matc &> /dev/null; then
    echo "错误: 找不到 matc 编译器"
    echo "请从 https://github.com/google/filament/releases 下载 Filament tools"
    echo "或者使用 brew install filament-tools"
    exit 1
fi

# 编译材质文件
echo "编译 simple_textured.filamat..."

# 编译为支持 Metal 的材质
matc \
    --platform=mobile \
    --api=metal \
    --target-api=metal \
    --optimize-size \
    --strip-debug \
    -o iOS3DRenderUI/simple_textured.filamat \
    iOS3DRenderUI/simple_textured.filamat

if [ $? -eq 0 ]; then
    echo "✅ 材质编译成功!"
    echo "生成文件: iOS3DRenderUI/simple_textured.filamat (已编译)"
else
    echo "❌ 材质编译失败"
    exit 1
fi

echo "材质编译完成"