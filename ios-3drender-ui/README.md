# iOS 3D 纹理渲染器

基于 Google Filament 引擎的 iOS 3D 纹理渲染演示应用。

## 功能特性

- ✅ 支持加载多张图片作为纹理
- ✅ 支持加载 3D 模型（glTF 格式）
- ✅ 动态纹理贴图和切换
- ✅ 手势控制纹理位置
- ✅ 纹理动画效果
- ✅ 多纹理选择和管理

## 系统要求

- iOS 14.0+
- Xcode 12.0+
- Swift 5.0+
- Metal 支持的设备

## 安装和构建

⚠️ **注意**：由于 Xcode 16.4+ 与 CocoaPods 存在兼容性问题，推荐使用以下方法：

### 方法一：自动构建（推荐）

```bash
cd ios-3drender-ui
./build.sh
```

如果 CocoaPods 安装失败，脚本会提供替代解决方案。

### 方法二：手动构建

```bash
./build-manual.sh
```

### 方法三：使用 XcodeGen + 手动依赖

1. 确保已安装 XcodeGen：
   ```bash
   brew install xcodegen
   ```

2. 生成 Xcode 项目：
   ```bash
   xcodegen generate
   ```

3. 手动添加 Filament 依赖：
   - 打开 `iOS3DRenderUI.xcodeproj`
   - File -> Add Package Dependencies
   - 添加：`https://github.com/google/filament`

### 方法四：完全手动创建

1. 打开 Xcode，创建新项目
2. 选择 Single View App 模板
3. 项目名称：iOS3DRenderUI
4. 语言：Swift，最低部署目标：iOS 14.0
5. 将 `iOS3DRenderUI/` 文件夹中的源文件添加到项目
6. 手动添加 Filament 依赖（方法同上）

## 使用说明

### 基本操作

1. **加载模型**：点击"加载模型"按钮，选择 glTF/GLB 格式的 3D 模型文件
2. **加载图片**：点击"加载图片"按钮，从相册选择一张或多张图片作为纹理
3. **选择纹理**：在底部的纹理列表中点击图片来切换当前纹理
4. **移动纹理**：在 3D 视图上拖拽来移动纹理位置
5. **纹理动画**：点击"开始动画"来启动自动纹理移动效果
6. **重置位置**：点击"重置位置"将纹理位置恢复到原点

### 支持的文件格式

- **3D 模型**：glTF (.gltf), GLB (.glb)
- **纹理图片**：PNG, JPEG, HEIC

## 项目结构

```
iOS3DRenderUI/
├── AppDelegate.swift          # 应用委托
├── SceneDelegate.swift        # 场景委托
├── MainViewController.swift   # 主界面控制器
├── FilamentRenderer.swift     # Filament 渲染器封装
└── Info.plist                # 应用配置
```

## 核心组件

### FilamentRenderer

负责 Filament 引擎的封装和 3D 渲染：

- 初始化 Filament 引擎和渲染组件
- 管理 3D 模型加载和显示
- 处理纹理加载和应用
- 控制纹理动画和变换
- 提供渲染循环

### MainViewController

主界面控制器，提供用户交互：

- Metal 视图显示 3D 内容
- 控制面板管理功能按钮
- 纹理选择集合视图
- 文件选择器集成
- 手势识别和处理

## 技术细节

### 渲染管线

1. **引擎初始化**：创建 Filament 引擎，选择 Metal 后端
2. **场景设置**：配置相机、光照和场景
3. **模型加载**：使用 AssetLoader 加载 glTF 模型
4. **纹理管理**：创建和管理 Filament 纹理对象
5. **材质应用**：动态创建和更新 PBR 材质
6. **渲染循环**：每帧渲染场景到 Metal 交换链

### 性能优化

- 异步加载模型和纹理
- Metal 硬件加速渲染
- 纹理内存管理和复用
- 60fps 渲染循环

## 依赖项

- **Filament**：Google 的 PBR 渲染引擎
- **MetalKit**：Metal 渲染框架
- **PhotosUI**：相册访问框架

## 故障排除

### 常见问题

1. **模型不显示**：
   - 检查模型格式是否为 glTF/GLB
   - 确认模型文件完整性
   - 查看控制台日志错误信息

2. **纹理不显示**：
   - 确认图片格式支持
   - 检查纹理是否成功加载
   - 验证材质是否正确应用

3. **性能问题**：
   - 检查模型复杂度
   - 监控纹理内存使用
   - 确认设备 Metal 支持

### 调试技巧

- 使用 Xcode Metal Debugger 分析渲染
- 监控内存使用情况
- 检查 Filament 日志输出

## 扩展功能

可以基于当前架构扩展的功能：

- 多模型同时显示
- 材质参数调节
- 光照控制
- 相机控制
- 模型动画播放
- 场景保存和加载

## 许可证

本项目仅用于学习和演示目的。Filament 引擎遵循 Apache 2.0 许可证。