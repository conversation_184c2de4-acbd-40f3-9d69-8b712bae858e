# 示例 3D 模型

这个文件夹用于存放测试用的 3D 模型文件。

## 推荐的模型格式

- **glTF 2.0** (.gltf + .bin + 纹理文件)
- **GLB** (.glb, 包含所有资源的二进制格式)

## 获取示例模型

你可以从以下网站下载免费的 glTF 模型用于测试：

1. **Khronos glTF Sample Models**
   - https://github.com/KhronosGroup/glTF-Sample-Models
   - 官方示例模型，质量高，兼容性好

2. **Sketchfab**
   - https://sketchfab.com/
   - 大量免费和付费模型，支持 glTF 导出

3. **Google Poly** (已关闭，但有镜像)
   - 简单的低多边形模型

## 推荐的测试模型

### 简单模型（适合初次测试）
- **立方体** (Cube)
- **球体** (Sphere)
- **茶壶** (Teapot)

### 复杂模型（测试性能）
- **头盔** (DamagedHelmet)
- **汽车** (Car models)
- **建筑** (Architecture)

## 使用说明

1. 将 glTF 或 GLB 文件下载到此文件夹
2. 在应用中点击"加载模型"按钮
3. 选择模型文件进行加载

## 注意事项

- 模型文件大小建议不超过 50MB
- 确保模型包含 UV 坐标以支持纹理贴图
- 复杂模型可能影响渲染性能