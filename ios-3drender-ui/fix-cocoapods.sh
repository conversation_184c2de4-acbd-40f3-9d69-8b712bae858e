#!/bin/bash

# iOS 3D 渲染器 - CocoaPods 兼容性问题修复脚本

set -e

echo "🔧 开始修复 CocoaPods 兼容性问题..."

# 1. 清理现有环境
echo "🧹 清理现有 CocoaPods 环境..."
if [ -f "Podfile.lock" ]; then
    rm -f Podfile.lock
fi

if [ -d "Pods" ]; then
    rm -rf Pods
fi

if [ -f "iOS3DRenderUI.xcworkspace" ]; then
    rm -rf iOS3DRenderUI.xcworkspace
fi

# 2. 更新 CocoaPods 到最新版本
echo "📦 更新 CocoaPods 到最新版本..."
echo "⚠️ 如需更新 CocoaPods，请手动运行："
echo "   sudo gem update cocoapods --no-document"
echo "正在更新 pod repo..."
pod repo update || echo "⚠️ pod repo 更新失败，继续..."

# 3. 清理 CocoaPods 缓存
echo "🗑️ 清理 CocoaPods 缓存..."
pod cache clean --all

# 4. 重新生成兼容的 Xcode 项目
echo "🔄 重新生成兼容的 Xcode 项目..."
if [ -d "iOS3DRenderUI.xcodeproj" ]; then
    rm -rf iOS3DRenderUI.xcodeproj
fi

# 使用兼容的 Xcode 版本生成项目
xcodegen generate

# 5. 修改项目格式为兼容版本
echo "⚙️ 修改项目格式为兼容版本..."
if [ -f "iOS3DRenderUI.xcodeproj/project.pbxproj" ]; then
    # 将 objectVersion 从 70 改为 56 (Xcode 14 兼容)
    sed -i '' 's/objectVersion = 70;/objectVersion = 56;/' iOS3DRenderUI.xcodeproj/project.pbxproj
    
    # 确保 archiveVersion 也兼容
    sed -i '' 's/archiveVersion = 1;/archiveVersion = 1;/' iOS3DRenderUI.xcodeproj/project.pbxproj
    
    echo "✅ 项目格式已修改为兼容版本"
else
    echo "❌ 未找到项目文件"
    exit 1
fi

# 6. 尝试安装 CocoaPods
echo "📚 尝试安装 CocoaPods 依赖..."
if pod install --verbose; then
    echo "✅ CocoaPods 安装成功！"
    WORKSPACE_FILE="iOS3DRenderUI.xcworkspace"
    SUCCESS=true
else
    echo "❌ CocoaPods 仍然失败，尝试备用方案..."
    SUCCESS=false
fi

# 7. 如果 CocoaPods 仍然失败，使用备用方案
if [ "$SUCCESS" = false ]; then
    echo "📦 使用 Swift Package Manager 备用方案..."
    
    # 删除 Podfile 相关文件
    rm -f Podfile Podfile.lock
    rm -rf Pods
    
    # 创建 Package.swift 用于 SPM
    cat > Package.swift << 'EOF'
// swift-tools-version:5.7
import PackageDescription

let package = Package(
    name: "iOS3DRenderUI",
    platforms: [
        .iOS(.v14)
    ],
    products: [
        .library(
            name: "iOS3DRenderUI",
            targets: ["iOS3DRenderUI"]
        ),
    ],
    dependencies: [
        // 注意：Filament 目前没有官方 SPM 支持
        // 需要手动集成或使用预编译框架
    ],
    targets: [
        .target(
            name: "iOS3DRenderUI",
            dependencies: []
        ),
    ]
)
EOF
    
    echo "📝 创建手动集成指南..."
    cat > MANUAL_INTEGRATION.md << 'EOF'
# 手动集成 Filament 框架

由于 CocoaPods 兼容性问题，请按以下步骤手动集成 Filament：

## 方法一：使用预编译框架

1. 下载 Filament 预编译框架：
   ```bash
   curl -L -o filament-ios.zip https://github.com/google/filament/releases/latest/download/filament-ios.zip
   unzip filament-ios.zip
   ```

2. 在 Xcode 中添加框架：
   - 打开 iOS3DRenderUI.xcodeproj
   - 选择项目 -> Target -> General -> Frameworks and Libraries
   - 点击 + 添加下载的 .framework 文件

## 方法二：从源码构建

1. 克隆 Filament 仓库：
   ```bash
   git clone https://github.com/google/filament.git
   cd filament
   ```

2. 构建 iOS 版本：
   ```bash
   ./build.sh -p ios
   ```

3. 将生成的框架添加到项目中

## 代码修改

在使用 Filament 时，确保导入语句正确：
```swift
import Filament
import FilamentIOS  // 如果使用预编译版本
```
EOF
    
    WORKSPACE_FILE="iOS3DRenderUI.xcodeproj"
fi

echo ""
echo "🎉 修复完成！"
echo ""

if [ "$SUCCESS" = true ]; then
    echo "✅ CocoaPods 集成成功"
    echo "📱 请打开 $WORKSPACE_FILE 开始开发"
else
    echo "⚠️ CocoaPods 集成失败，已切换到手动集成方案"
    echo "📱 请打开 $WORKSPACE_FILE"
    echo "📖 查看 MANUAL_INTEGRATION.md 了解手动集成步骤"
fi

# 可选：自动打开项目
read -p "是否要自动打开 Xcode 项目？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    open $WORKSPACE_FILE
fi