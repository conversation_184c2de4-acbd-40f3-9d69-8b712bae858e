#!/bin/bash

# iOS 3D 渲染器构建脚本

set -e

echo "🚀 开始构建 iOS 3D 渲染器项目..."

# 检查 XcodeGen 是否安装
if ! command -v xcodegen &> /dev/null; then
    echo "❌ XcodeGen 未安装，正在安装..."
    if command -v brew &> /dev/null; then
        brew install xcodegen
    else
        echo "❌ 请先安装 Homebrew，然后手动安装 XcodeGen"
        exit 1
    fi
fi

# 检查 CocoaPods 是否安装
if ! command -v pod &> /dev/null; then
    echo "❌ CocoaPods 未安装，正在安装..."
    sudo gem install cocoapods
fi

echo "📦 生成 Xcode 项目..."
xcodegen generate

echo "📚 安装 CocoaPods 依赖..."
if pod install; then
    echo "✅ CocoaPods 安装成功！"
    WORKSPACE_FILE="iOS3DRenderUI.xcworkspace"
else
    echo "❌ CocoaPods 安装失败 - 这是 Xcode 16.4+ 的已知兼容性问题"
    echo ""
    echo "🔧 解决方案："
    echo "1. 更新 CocoaPods 到最新版本："
    echo "   sudo gem update cocoapods"
    echo ""
    echo "2. 或者使用手动构建脚本："
    echo "   ./build-manual.sh"
    echo ""
    echo "3. 或者手动在 Xcode 中添加 Filament 依赖："
    echo "   - 打开 iOS3DRenderUI.xcodeproj"
    echo "   - File -> Add Package Dependencies"
    echo "   - 添加：https://github.com/google/filament"
    echo ""
    WORKSPACE_FILE="iOS3DRenderUI.xcodeproj"
fi

echo "✅ 项目生成完成！"
echo "📱 请打开 $WORKSPACE_FILE 开始开发"

# 可选：自动打开项目
read -p "是否要自动打开 Xcode 项目？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -f "iOS3DRenderUI.xcworkspace" ]; then
        open iOS3DRenderUI.xcworkspace
    else
        open iOS3DRenderUI.xcodeproj
    fi
fi