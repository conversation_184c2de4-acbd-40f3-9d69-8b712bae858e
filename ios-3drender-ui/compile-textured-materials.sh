#!/bin/bash

# Filament 纹理材质编译脚本
# 专门用于编译支持纹理贴图的材质

set -e

echo "🔨 开始编译纹理支持材质..."

# 检查材质源文件
MATERIAL_SOURCE="materials/textured_pbr.mat"
if [ ! -f "$MATERIAL_SOURCE" ]; then
    echo "❌ 材质源文件不存在: $MATERIAL_SOURCE"
    exit 1
fi

# 创建输出目录
OUTPUT_DIR="iOS3DRenderUI/Materials"
mkdir -p "$OUTPUT_DIR"

# 查找 matc 编译器
MATC_PATH=""

# 检查常见的 matc 位置
MATC_LOCATIONS=(
    "Pods/Filament/bin/matc"
    "Pods/Filament/out/release/filament/bin/matc"  
    "Pods/Filament/tools/matc"
    "Frameworks/filament-*/bin/matc"
    "/usr/local/bin/matc"
    "$(which matc 2>/dev/null)"
)

for location in "${MATC_LOCATIONS[@]}"; do
    if [ -f "$location" ] && [ -x "$location" ]; then
        MATC_PATH="$location"
        echo "✅ 找到 matc 编译器: $MATC_PATH"
        break
    fi
done

# 如果没有找到 matc，尝试从 Filament 发布包下载
if [ -z "$MATC_PATH" ]; then
    echo "⚠️ 未找到 matc 编译器，正在下载..."
    
    # 创建 tools 目录
    mkdir -p tools
    cd tools
    
    # 下载适用于 macOS 的 Filament 工具
    FILAMENT_VERSION="1.56.6"
    TOOLS_URL="https://github.com/google/filament/releases/download/v${FILAMENT_VERSION}/filament-v${FILAMENT_VERSION}-mac.tgz"
    
    echo "正在下载 Filament 工具包..."
    if curl -L -o filament-tools.tgz "$TOOLS_URL"; then
        echo "✅ 下载成功，正在解压..."
        tar -xzf filament-tools.tgz
        rm filament-tools.tgz
        
        # 查找解压后的 matc
        MATC_PATH="$(find . -name "matc" -type f -executable | head -1)"
        if [ -n "$MATC_PATH" ]; then
            MATC_PATH="$(pwd)/$MATC_PATH"
            echo "✅ matc 工具准备就绪: $MATC_PATH"
        else
            echo "❌ 解压后未找到 matc 工具"
            cd ..
            exit 1
        fi
    else
        echo "❌ 下载失败，请手动安装 Filament 工具"
        echo "📝 请访问: https://github.com/google/filament/releases"
        cd ..
        exit 1
    fi
    
    cd ..
fi

# 检查 matc 是否可执行
if [ ! -x "$MATC_PATH" ]; then
    echo "❌ matc 不可执行: $MATC_PATH"
    exit 1
fi

echo "🔧 使用 matc 编译器: $MATC_PATH"

# 编译纹理支持材质
OUTPUT_FILE="$OUTPUT_DIR/textured_pbr.filamat"

echo "📦 编译材质: $MATERIAL_SOURCE -> $OUTPUT_FILE"

# 编译命令，针对 iOS Metal 优化
"$MATC_PATH" \
    --platform=mobile \
    --api=metal \
    --target-api=metal \
    --optimize-size \
    --optimize-performance \
    --strip-debug \
    --output="$OUTPUT_FILE" \
    "$MATERIAL_SOURCE"

# 检查编译结果
if [ -f "$OUTPUT_FILE" ]; then
    FILE_SIZE=$(du -h "$OUTPUT_FILE" | cut -f1)
    echo "✅ 材质编译成功!"
    echo "📁 输出文件: $OUTPUT_FILE (大小: $FILE_SIZE)"
    
    # 验证编译后的材质文件
    echo "🔍 验证编译后的材质文件..."
    if [ -s "$OUTPUT_FILE" ]; then
        echo "✅ 材质文件验证通过"
    else
        echo "❌ 材质文件为空或损坏"
        exit 1
    fi
else
    echo "❌ 材质编译失败，输出文件不存在"
    exit 1
fi

# 创建材质使用说明
cat > "$OUTPUT_DIR/README.md" << 'EOF'
# 编译后的材质文件

## 文件说明

- `textured_pbr.filamat` - 支持纹理贴图的 PBR 材质

## 材质特性

- **着色模型**: 物理基础渲染 (PBR)
- **纹理支持**: 完整的 baseColorMap 支持
- **参数控制**: 金属度、粗糙度、纹理偏移和缩放
- **性能优化**: 针对 iOS Metal 后端优化

## 使用方法

在 FilamentWrapper.mm 中加载此材质：

```cpp
// 加载预编译材质
NSString *materialPath = [[NSBundle mainBundle] pathForResource:@"textured_pbr" ofType:@"filamat" inDirectory:@"Materials"];
NSData *materialData = [NSData dataWithContentsOfFile:materialPath];
Material *material = Material::Builder()
    .package(materialData.bytes, materialData.length)
    .build(*_engine);
```
EOF

echo ""
echo "🎉 材质编译完成！"
echo "📋 下一步:"
echo "   1. 将编译后的材质集成到 iOS 项目中"
echo "   2. 修改 FilamentWrapper.mm 使用新材质"
echo "   3. 测试真正的纹理贴图功能"
echo ""
echo "📖 查看使用说明: $OUTPUT_DIR/README.md"