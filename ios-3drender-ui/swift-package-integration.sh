#!/bin/bash

# iOS 3D 渲染器 - Swift Package Manager 集成脚本

set -e

echo "📦 开始 Swift Package Manager 集成..."

# 1. 清理 CocoaPods 相关文件
echo "🧹 清理 CocoaPods 相关文件..."
if [ -f "Podfile" ]; then
    rm -f Podfile
fi

if [ -f "Podfile.lock" ]; then
    rm -f Podfile.lock
fi

if [ -d "Pods" ]; then
    rm -rf Pods
fi

if [ -f "iOS3DRenderUI.xcworkspace" ]; then
    rm -rf iOS3DRenderUI.xcworkspace
fi

# 2. 重新生成 Xcode 项目
echo "🔄 重新生成 Xcode 项目..."
if [ -d "iOS3DRenderUI.xcodeproj" ]; then
    rm -rf iOS3DRenderUI.xcodeproj
fi

xcodegen generate

# 3. 下载预编译的 Filament 框架
echo "⬇️ 下载 Filament 预编译框架..."
FILAMENT_VERSION="1.56.6"
FILAMENT_URL="https://github.com/google/filament/releases/download/v${FILAMENT_VERSION}/filament-v${FILAMENT_VERSION}-ios.tgz"

if [ ! -d "Frameworks" ]; then
    mkdir -p Frameworks
fi

cd Frameworks

echo "正在下载 Filament v${FILAMENT_VERSION}..."
if curl -L -o filament-ios.tgz "$FILAMENT_URL"; then
    echo "✅ 下载成功，正在解压..."
    tar -xzf filament-ios.tgz
    rm filament-ios.tgz
    
    # 重命名解压后的文件夹
    if [ -d "filament" ]; then
        mv filament filament-${FILAMENT_VERSION}
    fi
    
    echo "✅ Filament 框架已准备就绪"
else
    echo "❌ 下载失败，尝试从备用源下载..."
    
    # 备用下载方法 - 从 CDN
    BACKUP_URL="https://storage.googleapis.com/filament-releases/v${FILAMENT_VERSION}/filament-v${FILAMENT_VERSION}-ios.tgz"
    if curl -L -o filament-ios.tgz "$BACKUP_URL"; then
        tar -xzf filament-ios.tgz
        rm filament-ios.tgz
        if [ -d "filament" ]; then
            mv filament filament-${FILAMENT_VERSION}
        fi
        echo "✅ 从备用源下载成功"
    else
        echo "❌ 无法下载 Filament 框架，请手动下载"
        echo "📝 请访问：https://github.com/google/filament/releases"
        cd ..
        exit 1
    fi
fi

cd ..

# 4. 创建框架集成脚本
echo "📝 创建框架集成指南..."
cat > FRAMEWORKS_INTEGRATION.md << 'EOF'
# Filament 框架手动集成指南

## 已为您准备的文件

- `Frameworks/filament-*/` - 预编译的 Filament 框架

## 在 Xcode 中集成步骤

### 1. 添加框架到项目

1. 打开 `iOS3DRenderUI.xcodeproj`
2. 选择项目根节点 -> 选择 Target "iOS3DRenderUI"
3. 切换到 "General" 选项卡
4. 在 "Frameworks, Libraries, and Embedded Content" 部分点击 "+"

### 2. 添加 Filament 框架

添加以下框架文件（从 `Frameworks/filament-*/lib/universal/` 目录）：

**必需的核心框架：**
- `libfilament.a` - 核心渲染引擎
- `libfilabridge.a` - 桥接库
- `libfilaflat.a` - 材质系统
- `libbackend.a` - 渲染后端
- `libutils.a` - 工具库

**glTF 支持（推荐）：**
- `libgltfio_core.a` - glTF 加载器
- `libdracodec.a` - Draco 压缩支持

**其他有用的库：**
- `libimage.a` - 图像处理
- `libgeometry.a` - 几何工具
- `libmath.a` - 数学库

### 3. 添加头文件搜索路径

1. 选择项目 -> Target -> Build Settings
2. 搜索 "Header Search Paths"
3. 添加路径：`$(PROJECT_DIR)/Frameworks/filament-*/include`
4. 设置为 "recursive" 模式

### 4. 添加库搜索路径

1. 在 Build Settings 中搜索 "Library Search Paths"
2. 添加路径：`$(PROJECT_DIR)/Frameworks/filament-*/lib/universal`

### 5. 链接设置

在 "Other Linker Flags" 中添加：
```
-lc++
-ObjC
```

### 6. 验证集成

在你的 Swift 代码中测试导入：

```swift
// 在 FilamentRenderer.swift 中添加这些导入
import Foundation
import MetalKit

// C++ 桥接头文件（需要创建）
// 参见下面的桥接头文件设置
```

## 创建 Objective-C++ 桥接

由于 Filament 是 C++ 库，需要创建 Objective-C++ 桥接：

### 1. 创建桥接头文件

创建 `iOS3DRenderUI-Bridging-Header.h`：

```objc
#ifndef iOS3DRenderUI_Bridging_Header_h
#define iOS3DRenderUI_Bridging_Header_h

// Filament C++ 头文件
#ifdef __cplusplus
#include <filament/Engine.h>
#include <filament/Renderer.h>
#include <filament/Scene.h>
#include <filament/View.h>
#include <filament/Camera.h>
#include <filament/Material.h>
#include <filament/MaterialInstance.h>
#include <filament/Texture.h>
#include <filament/SwapChain.h>
#include <gltfio/AssetLoader.h>
#include <gltfio/ResourceLoader.h>
#include <gltfio/FilamentAsset.h>
#endif

#endif
```

### 2. 在 Build Settings 中设置桥接头文件

- 搜索 "Objective-C Bridging Header"
- 设置为：`iOS3DRenderUI/iOS3DRenderUI-Bridging-Header.h`

## 故障排除

### 链接错误
如果遇到链接错误，尝试：
1. 确保所有必需的 .a 文件都已添加
2. 检查库搜索路径是否正确
3. 添加 `-lc++` 到链接器标志

### 头文件找不到
1. 验证头文件搜索路径
2. 确保路径是 recursive 的
3. 检查框架解压是否完整

### 运行时错误
1. 确保 Metal 在设备上可用
2. 检查 iOS 部署目标设置
3. 验证框架版本兼容性
EOF

# 5. 创建 C++ 桥接文件模板
echo "🌉 创建 C++ 桥接文件模板..."
cat > iOS3DRenderUI/FilamentBridge.h << 'EOF'
//
//  FilamentBridge.h
//  iOS3DRenderUI
//
//  Objective-C++ 桥接文件，用于在 Swift 中使用 Filament C++ API
//

#ifndef FilamentBridge_h
#define FilamentBridge_h

#import <Foundation/Foundation.h>
#import <MetalKit/MetalKit.h>

// 前向声明 C++ 类，避免在头文件中包含 C++
namespace filament {
    class Engine;
    class Renderer;
    class Scene;
    class View;
    class Camera;
    class SwapChain;
}

// Objective-C 包装器类
@interface FilamentEngineWrapper : NSObject

- (instancetype)init;
- (void)dealloc;
- (BOOL)setupWithMetalLayer:(CAMetalLayer *)metalLayer;
- (void)render;
- (void)resizeWithWidth:(NSUInteger)width height:(NSUInteger)height;

@end

#endif /* FilamentBridge_h */
EOF

cat > iOS3DRenderUI/FilamentBridge.mm << 'EOF'
//
//  FilamentBridge.mm
//  iOS3DRenderUI
//
//  Filament C++ 桥接实现
//

#import "FilamentBridge.h"

// 包含 Filament C++ 头文件
#include <filament/Engine.h>
#include <filament/Renderer.h>
#include <filament/Scene.h>
#include <filament/View.h>
#include <filament/Camera.h>
#include <filament/SwapChain.h>
#include <backend/Platform.h>

using namespace filament;

@interface FilamentEngineWrapper () {
    Engine* _engine;
    Renderer* _renderer;
    Scene* _scene;
    View* _view;
    Camera* _camera;
    SwapChain* _swapChain;
}
@end

@implementation FilamentEngineWrapper

- (instancetype)init {
    self = [super init];
    if (self) {
        // 创建 Filament 引擎
        _engine = Engine::create(Engine::Backend::METAL);
        if (!_engine) {
            return nil;
        }
        
        // 创建渲染器
        _renderer = _engine->createRenderer();
        
        // 创建场景
        _scene = _engine->createScene();
        
        // 创建视图
        _view = _engine->createView();
        _view->setScene(_scene);
        
        // 创建相机
        auto cameraEntity = _engine->getEntityManager().create();
        _camera = _engine->createCamera(cameraEntity);
        _view->setCamera(_camera);
    }
    return self;
}

- (void)dealloc {
    if (_engine) {
        if (_swapChain) {
            _engine->destroy(_swapChain);
        }
        _engine->destroy(_camera);
        _engine->destroy(_view);
        _engine->destroy(_scene);
        _engine->destroy(_renderer);
        Engine::destroy(_engine);
    }
}

- (BOOL)setupWithMetalLayer:(CAMetalLayer *)metalLayer {
    if (!_engine || !metalLayer) {
        return NO;
    }
    
    // 创建交换链
    _swapChain = _engine->createSwapChain((void*)metalLayer);
    return _swapChain != nullptr;
}

- (void)render {
    if (!_renderer || !_swapChain || !_view) {
        return;
    }
    
    if (_renderer->beginFrame(_swapChain)) {
        _renderer->render(_view);
        _renderer->endFrame();
    }
}

- (void)resizeWithWidth:(NSUInteger)width height:(NSUInteger)height {
    if (_view) {
        _view->setViewport({0, 0, (uint32_t)width, (uint32_t)height});
    }
    
    if (_camera) {
        double aspect = (double)width / (double)height;
        _camera->setProjection(45.0, aspect, 0.1, 100.0);
    }
}

@end
EOF

echo ""
echo "✅ Swift Package Manager 集成完成！"
echo ""
echo "📋 接下来的步骤：" 
echo "1. 打开 iOS3DRenderUI.xcodeproj"
echo "2. 按照 FRAMEWORKS_INTEGRATION.md 中的步骤集成框架"
echo "3. 将 FilamentRenderer.swift 中的代码迁移到使用 FilamentBridge"
echo ""
echo "📖 详细说明请查看："
echo "   - FRAMEWORKS_INTEGRATION.md"
echo "   - iOS3DRenderUI/FilamentBridge.h"
echo "   - iOS3DRenderUI/FilamentBridge.mm"

# 可选：自动打开项目
read -p "是否要自动打开 Xcode 项目？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    open iOS3DRenderUI.xcodeproj
fi