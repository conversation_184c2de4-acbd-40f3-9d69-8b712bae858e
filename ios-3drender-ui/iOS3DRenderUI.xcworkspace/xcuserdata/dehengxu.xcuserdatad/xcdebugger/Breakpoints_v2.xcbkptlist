<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "B2F10100-5032-4631-A085-67F43C12F8CA"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B0009462-8E46-4BF5-BCD6-2950B1CE6654"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "iOS3DRenderUI/MainViewController.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "323"
            endingLineNumber = "323"
            landmarkName = "picker(_:didFinishPicking:)"
            landmarkType = "7">
            <Locations>
               <Location
                  uuid = "B0009462-8E46-4BF5-BCD6-2950B1CE6654 - 9cd57ed2c1608c81"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 @Swift.MainActor () -&gt; () in closure #1 @Sendable (Swift.Optional&lt;__C.NSItemProviderReading&gt;, Swift.Optional&lt;Swift.Error&gt;) -&gt; () in iOS3DRenderUI.MainViewController.picker(_: __C.PHPickerViewController, didFinishPicking: Swift.Array&lt;PhotosUI.PHPickerResult&gt;) -&gt; ()"
                  moduleName = "iOS3DRenderUI.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Projects/personal/Filamenet-1/ios-3drender-ui/iOS3DRenderUI/MainViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "323"
                  endingLineNumber = "323">
               </Location>
               <Location
                  uuid = "B0009462-8E46-4BF5-BCD6-2950B1CE6654 - d8a9832d21f1ce4f"
                  shouldBeEnabled = "Yes"
                  ignoreCount = "0"
                  continueAfterRunningActions = "No"
                  symbolName = "closure #1 (Swift.Bool) -&gt; () in closure #1 @Swift.MainActor () -&gt; () in closure #1 @Sendable (Swift.Optional&lt;__C.NSItemProviderReading&gt;, Swift.Optional&lt;Swift.Error&gt;) -&gt; () in iOS3DRenderUI.MainViewController.picker(_: __C.PHPickerViewController, didFinishPicking: Swift.Array&lt;PhotosUI.PHPickerResult&gt;) -&gt; ()"
                  moduleName = "iOS3DRenderUI.debug.dylib"
                  usesParentBreakpointCondition = "Yes"
                  urlString = "file:///Users/<USER>/Projects/personal/Filamenet-1/ios-3drender-ui/iOS3DRenderUI/MainViewController.swift"
                  startingColumnNumber = "9223372036854775807"
                  endingColumnNumber = "9223372036854775807"
                  startingLineNumber = "316"
                  endingLineNumber = "316">
               </Location>
            </Locations>
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "E3E69EA8-85C6-4684-B403-C2D4FC99FA3C"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "iOS3DRenderUI/FilamentWrapper.mm"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1004"
            endingLineNumber = "1004"
            landmarkName = "-updateMaterialTexture"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "268801E9-1ACD-4B4F-B72B-412CA10CD142"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "iOS3DRenderUI/FilamentWrapper.mm"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "998"
            endingLineNumber = "998"
            landmarkName = "-updateMaterialTexture"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "CE8492EA-6A9C-4DFA-B36E-07EDA16618EE"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "iOS3DRenderUI/FilamentWrapper.mm"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "749"
            endingLineNumber = "749"
            landmarkName = "-tryApplyTextureToMaterial:texture:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2DCCCB5B-18CE-47C8-A19E-1C2B3122D735"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "iOS3DRenderUI/FilamentWrapper.mm"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "751"
            endingLineNumber = "751"
            landmarkName = "-tryApplyTextureToMaterial:texture:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "5ADB799D-F53D-465E-BAA3-2BB24699578B"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "iOS3DRenderUI/FilamentWrapper.mm"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "770"
            endingLineNumber = "770"
            landmarkName = "-tryApplyTextureToMaterial:texture:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "84236A88-1689-4D58-B2ED-4D1009A50696"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "iOS3DRenderUI/FilamentWrapper.mm"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "768"
            endingLineNumber = "768"
            landmarkName = "-tryApplyTextureToMaterial:texture:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B770A44B-6D59-4596-9B10-297F8D3BB962"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "iOS3DRenderUI/FilamentWrapper.mm"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "324"
            endingLineNumber = "324"
            landmarkName = "-loadModelFromData:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "37A8ACBB-D5DB-486A-BAAB-9B31C033A3F9"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "iOS3DRenderUI/FilamentWrapper.mm"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "339"
            endingLineNumber = "339"
            landmarkName = "-loadModelFromData:"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
