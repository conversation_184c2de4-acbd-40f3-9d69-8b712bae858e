# 示例纹理图片

这个文件夹用于存放测试用的纹理图片。

## 支持的图片格式

- **PNG** (.png) - 支持透明度
- **JPEG** (.jpg, .jpeg) - 标准格式
- **HEIC** (.heic) - iOS 高效格式

## 纹理类型示例

### 基础纹理
- **木纹** (Wood grain)
- **石材** (Stone textures)
- **金属** (Metal surfaces)
- **布料** (Fabric patterns)

### 测试图案
- **棋盘格** (Checkerboard) - 测试 UV 映射
- **渐变** (Gradients) - 测试插值
- **网格** (Grid) - 测试变形

### 图片素材网站

1. **免费纹理**
   - https://www.textures.com/ (CC0 纹理)
   - https://freepbr.com/ (PBR 材质)
   - https://3dtextures.me/ (无缝纹理)

2. **测试图案生成**
   - https://www.3dtextures.me/checker/ (在线棋盘格生成)
   - Photoshop/GIMP 等图像编辑软件

## 使用建议

### 纹理分辨率
- **512x512** - 低分辨率，适合简单测试
- **1024x1024** - 中等分辨率，平衡质量和性能
- **2048x2048** - 高分辨率，高质量显示

### 性能考虑
- 建议使用 2 的幂次方分辨率 (256, 512, 1024, 2048)
- 过大的纹理会影响内存使用和加载速度
- 多张纹理同时使用时注意总内存占用

## 应用中的使用

1. 将图片放入此文件夹
2. 在应用中点击"加载图片"按钮
3. 从相册选择图片或直接选择文件
4. 在纹理列表中切换不同纹理
5. 使用手势移动纹理位置

## 创建自定义纹理

### 简单测试纹理
```python
# Python 脚本生成棋盘格纹理
from PIL import Image
import numpy as np

def create_checkerboard(size=512, squares=8):
    board = np.zeros((size, size, 3), dtype=np.uint8)
    square_size = size // squares
    
    for i in range(squares):
        for j in range(squares):
            if (i + j) % 2 == 0:
                y1, y2 = i * square_size, (i + 1) * square_size
                x1, x2 = j * square_size, (j + 1) * square_size
                board[y1:y2, x1:x2] = 255
    
    Image.fromarray(board).save('checkerboard.png')

create_checkerboard()
```

### 渐变纹理
```python
def create_gradient(size=512):
    gradient = np.zeros((size, size, 3), dtype=np.uint8)
    for i in range(size):
        gradient[i, :] = [i * 255 // size, 0, 255 - i * 255 // size]
    
    Image.fromarray(gradient).save('gradient.png')

create_gradient()
```