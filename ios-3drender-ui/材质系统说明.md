# iOS 3D 渲染项目 - 材质系统使用说明

## 📍 当前状态

由于 Filament 的默认材质不支持纹理参数，我们实现了一个**智能纹理模拟系统**，通过颜色变化来表示不同的纹理效果。

## 🎨 材质系统工作原理

### 1. 材质创建流程
```
createSimpleMaterial() 
    ↓
loadCustomMaterial() [尝试加载预编译材质]
    ↓ (失败)
使用默认材质 + 运行时优化
```

### 2. 纹理应用策略
```
tryApplyTextureToMaterial()
    ↓
检查支持的纹理参数: baseColorMap, diffuseMap, albedoMap...
    ↓ (默认材质不支持)
simulateTextureWithColor() [智能颜色模拟]
```

## 🌈 纹理颜色映射

我们为不同的纹理索引定义了代表性颜色：

| 索引 | 颜色 | 模拟纹理类型 | RGB 值 |
|------|------|-------------|--------|
| 0 | 温暖棕色 | 木纹纹理 | (0.8, 0.4, 0.2) |
| 1 | 翠绿色 | 草地纹理 | (0.2, 0.7, 0.3) |
| 2 | 天蓝色 | 天空纹理 | (0.3, 0.5, 0.9) |
| 3 | 金黄色 | 金属纹理 | (0.9, 0.8, 0.3) |
| 4 | 紫色 | 神秘纹理 | (0.6, 0.3, 0.8) |
| 5 | 粉红色 | 花朵纹理 | (0.9, 0.5, 0.6) |
| 6 | 灰色 | 石头纹理 | (0.4, 0.4, 0.4) |
| 7 | 橙色 | 火焰纹理 | (0.9, 0.6, 0.2) |

## 🔍 日志输出说明

运行应用时，您会看到以下关键日志：

### 材质创建日志
```
🔧 尝试创建运行时材质...
创建运行时材质基于: Filament Default Material
  ✅ 设置 baseColor 支持
  ✅ 设置 metallicFactor = 0.0
  ✅ 设置 roughnessFactor = 0.3
✅ 运行时材质创建成功，支持颜色变化模拟纹理
```

### 纹理应用日志
```
🔍 材质支持的纹理参数: ()
⚠️ 材质不支持任何纹理参数，将使用颜色方案模拟纹理
🎨 使用颜色变化模拟纹理效果 (纹理索引: 0)
✅ 设置模拟纹理颜色 baseColor: (0.80, 0.40, 0.20)
🎨 纹理颜色模拟成功，使用颜色索引 0
```

## 📱 用户体验

1. **加载图片** - 用户可以正常加载图片纹理
2. **纹理切换** - 切换纹理时，球体会变成对应纹理的代表颜色
3. **视觉反馈** - 不同的纹理用不同的颜色和材质属性表示
4. **PBR 效果** - 自动调整金属度和粗糙度，提供丰富的视觉效果

## 🚀 未来改进方向

### 1. 预编译材质支持
- 使用 Filament 材质编译器 (`matc`) 创建支持纹理的自定义材质
- 将编译好的 `.filamat` 文件添加到项目资源中

### 2. 纹理颜色提取
- 实现从实际纹理图像中提取主要颜色的算法
- 使用真实的纹理颜色信息而不是预定义颜色

### 3. 更高级的材质效果
- 支持法线贴图、粗糙度贴图等
- 实现更复杂的材质混合效果

## 🛠️ 开发者说明

### 关键方法

1. **`createSimpleMaterial`** - 创建优化的材质实例
2. **`loadCustomMaterial`** - 尝试加载自定义材质（当前为运行时版本）
3. **`simulateTextureWithColor`** - 通过颜色模拟纹理效果
4. **`tryApplyTextureToMaterial`** - 智能纹理应用逻辑

### 配置参数

- **金属度**：根据纹理类型调整（金属纹理 = 0.8，其他 = 0.1）
- **粗糙度**：0.3-0.7 范围，根据纹理索引动态调整
- **颜色空间**：使用 sRGB 颜色空间以获得更好的视觉效果

## ✨ 总结

虽然当前无法直接应用纹理贴图，但我们的智能颜色模拟系统提供了直观的纹理表示：
- ✅ 支持多纹理加载和切换
- ✅ 智能颜色映射系统
- ✅ 动态 PBR 参数调整  
- ✅ 丰富的调试日志输出
- ✅ 未来扩展性良好

这个解决方案在等待完整纹理支持的同时，为用户提供了完整的纹理切换体验！