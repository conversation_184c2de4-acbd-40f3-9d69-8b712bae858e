// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 63;
	objects = {

/* Begin PBXBuildFile section */
		24D46AA52E40869500EBEEBD /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 24D46AA42E40869500EBEEBD /* Main.storyboard */; };
		24D46AAB2E41B01A00EBEEBD /* simple_textured.filamat in Resources */ = {isa = PBXBuildFile; fileRef = 24D46AAA2E41B01A00EBEEBD /* simple_textured.filamat */; };
		24D46AAC2E41B01A00EBEEBD /* simple_material.mat in Resources */ = {isa = PBXBuildFile; fileRef = 24D46AA92E41B01A00EBEEBD /* simple_material.mat */; };
		24D46AAE2E41B01A00EBEEBD /* FilamentMaterialManager.mm in Sources */ = {isa = PBXBuildFile; fileRef = 24D46AA72E41B01A00EBEEBD /* FilamentMaterialManager.mm */; };
		268330EFDE4569CDC07A2685 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2763E0ED5C7EC54F1ABD5230 /* AppDelegate.swift */; };
		27541977BA817B3CED3DC3F1 /* FilamentWrapper.mm in Sources */ = {isa = PBXBuildFile; fileRef = 866DDE086C2BEBD9F2E2D1C3 /* FilamentWrapper.mm */; };
		4E211C45BFDBA37CFD775ACE /* Pods_iOS3DRenderUITests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0BB26B99888CACC26E400819 /* Pods_iOS3DRenderUITests.framework */; };
		68D0696DF07B20D98D3B5C26 /* iOS3DRenderUITests.swift in Sources */ = {isa = PBXBuildFile; fileRef = F7BD4DEA17D50E9577918EC2 /* iOS3DRenderUITests.swift */; };
		92E797756541874FDC54175A /* MainViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E7D96E9BF7E2A738DF54F2F /* MainViewController.swift */; };
		BE214CF4E22484755D1C3F31 /* FilamentRenderer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 726A6948DE37020ED5B25EC7 /* FilamentRenderer.swift */; };
		CB71DA94D90AC2BAFC5922BF /* Pods_iOS3DRenderUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E25BC33BBD896C62A02C46B6 /* Pods_iOS3DRenderUI.framework */; };
		DEF15073ED4BBF4C8DE1D4D0 /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = F9FCD2E95D59AD5677600C9A /* SceneDelegate.swift */; };
		E67EA5AAF19817B825356DEA /* SimpleMaterial.mat in Resources */ = {isa = PBXBuildFile; fileRef = 7D3E007CB0E9D81002428E1B /* SimpleMaterial.mat */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		8E2A08E8050AE0F9B410CC75 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 4BA32296A5CE703B1A159B71 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2671C2E54EFBC5C4EA504ABC;
			remoteInfo = iOS3DRenderUI;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		04A607EA550271F895792CD7 /* iOS3DRenderUI-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "iOS3DRenderUI-Bridging-Header.h"; sourceTree = "<group>"; };
		0BB26B99888CACC26E400819 /* Pods_iOS3DRenderUITests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_iOS3DRenderUITests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		24D46AA42E40869500EBEEBD /* Main.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = Main.storyboard; sourceTree = "<group>"; };
		24D46AA62E41B01A00EBEEBD /* FilamentMaterialManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FilamentMaterialManager.h; sourceTree = "<group>"; };
		24D46AA72E41B01A00EBEEBD /* FilamentMaterialManager.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = FilamentMaterialManager.mm; sourceTree = "<group>"; };
		24D46AA92E41B01A00EBEEBD /* simple_material.mat */ = {isa = PBXFileReference; lastKnownFileType = text; path = simple_material.mat; sourceTree = "<group>"; };
		24D46AAA2E41B01A00EBEEBD /* simple_textured.filamat */ = {isa = PBXFileReference; lastKnownFileType = text; path = simple_textured.filamat; sourceTree = "<group>"; };
		2763E0ED5C7EC54F1ABD5230 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		4130055BF880D06EB29EE22A /* FilamentWrapper.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FilamentWrapper.h; sourceTree = "<group>"; };
		4EB9308F7A4F21F1F0789EBF /* iOS3DRenderUI.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = iOS3DRenderUI.app; sourceTree = BUILT_PRODUCTS_DIR; };
		726A6948DE37020ED5B25EC7 /* FilamentRenderer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilamentRenderer.swift; sourceTree = "<group>"; };
		7D3E007CB0E9D81002428E1B /* SimpleMaterial.mat */ = {isa = PBXFileReference; lastKnownFileType = text; path = SimpleMaterial.mat; sourceTree = "<group>"; };
		866DDE086C2BEBD9F2E2D1C3 /* FilamentWrapper.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = FilamentWrapper.mm; sourceTree = "<group>"; };
		8E7D96E9BF7E2A738DF54F2F /* MainViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainViewController.swift; sourceTree = "<group>"; };
		90732D0DEC444A4912734741 /* Pods-iOS3DRenderUI.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iOS3DRenderUI.release.xcconfig"; path = "Target Support Files/Pods-iOS3DRenderUI/Pods-iOS3DRenderUI.release.xcconfig"; sourceTree = "<group>"; };
		C5B2513C8D044109D44A89CC /* Pods-iOS3DRenderUITests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iOS3DRenderUITests.release.xcconfig"; path = "Target Support Files/Pods-iOS3DRenderUITests/Pods-iOS3DRenderUITests.release.xcconfig"; sourceTree = "<group>"; };
		DA7AAA9C6C3F9CF80FA34033 /* Pods-iOS3DRenderUI.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iOS3DRenderUI.debug.xcconfig"; path = "Target Support Files/Pods-iOS3DRenderUI/Pods-iOS3DRenderUI.debug.xcconfig"; sourceTree = "<group>"; };
		E25BC33BBD896C62A02C46B6 /* Pods_iOS3DRenderUI.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_iOS3DRenderUI.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		E4915DB100A5350C1B5FB1D1 /* iOS3DRenderUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = iOS3DRenderUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F4EAABDFE781F2FB9CBC2E50 /* Pods-iOS3DRenderUITests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-iOS3DRenderUITests.debug.xcconfig"; path = "Target Support Files/Pods-iOS3DRenderUITests/Pods-iOS3DRenderUITests.debug.xcconfig"; sourceTree = "<group>"; };
		F7BD4DEA17D50E9577918EC2 /* iOS3DRenderUITests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = iOS3DRenderUITests.swift; sourceTree = "<group>"; };
		F9FCD2E95D59AD5677600C9A /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		7A31935C9955C5BDB15308BB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				CB71DA94D90AC2BAFC5922BF /* Pods_iOS3DRenderUI.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DCED519963E1EE581A50FDA7 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				4E211C45BFDBA37CFD775ACE /* Pods_iOS3DRenderUITests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2363C13E613BAB3CF40C93C5 /* iOS3DRenderUITests */ = {
			isa = PBXGroup;
			children = (
				F7BD4DEA17D50E9577918EC2 /* iOS3DRenderUITests.swift */,
			);
			path = iOS3DRenderUITests;
			sourceTree = "<group>";
		};
		5382C5B218CE7EE9B8E8EE70 /* iOS3DRenderUI */ = {
			isa = PBXGroup;
			children = (
				24D46AA62E41B01A00EBEEBD /* FilamentMaterialManager.h */,
				24D46AA72E41B01A00EBEEBD /* FilamentMaterialManager.mm */,
				24D46AA92E41B01A00EBEEBD /* simple_material.mat */,
				24D46AAA2E41B01A00EBEEBD /* simple_textured.filamat */,
				2763E0ED5C7EC54F1ABD5230 /* AppDelegate.swift */,
				726A6948DE37020ED5B25EC7 /* FilamentRenderer.swift */,
				4130055BF880D06EB29EE22A /* FilamentWrapper.h */,
				866DDE086C2BEBD9F2E2D1C3 /* FilamentWrapper.mm */,
				04A607EA550271F895792CD7 /* iOS3DRenderUI-Bridging-Header.h */,
				8E7D96E9BF7E2A738DF54F2F /* MainViewController.swift */,
				F9FCD2E95D59AD5677600C9A /* SceneDelegate.swift */,
				7D3E007CB0E9D81002428E1B /* SimpleMaterial.mat */,
				24D46AA42E40869500EBEEBD /* Main.storyboard */,
			);
			path = iOS3DRenderUI;
			sourceTree = "<group>";
		};
		5449506CD423FC1322F91F52 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				E25BC33BBD896C62A02C46B6 /* Pods_iOS3DRenderUI.framework */,
				0BB26B99888CACC26E400819 /* Pods_iOS3DRenderUITests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		745CA0ED088FCE068278F0E3 = {
			isa = PBXGroup;
			children = (
				5382C5B218CE7EE9B8E8EE70 /* iOS3DRenderUI */,
				2363C13E613BAB3CF40C93C5 /* iOS3DRenderUITests */,
				F0CCE10A537D7A12D94B1113 /* Products */,
				B9DB89467816D1CE52F7269F /* Pods */,
				5449506CD423FC1322F91F52 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		B9DB89467816D1CE52F7269F /* Pods */ = {
			isa = PBXGroup;
			children = (
				DA7AAA9C6C3F9CF80FA34033 /* Pods-iOS3DRenderUI.debug.xcconfig */,
				90732D0DEC444A4912734741 /* Pods-iOS3DRenderUI.release.xcconfig */,
				F4EAABDFE781F2FB9CBC2E50 /* Pods-iOS3DRenderUITests.debug.xcconfig */,
				C5B2513C8D044109D44A89CC /* Pods-iOS3DRenderUITests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		F0CCE10A537D7A12D94B1113 /* Products */ = {
			isa = PBXGroup;
			children = (
				4EB9308F7A4F21F1F0789EBF /* iOS3DRenderUI.app */,
				E4915DB100A5350C1B5FB1D1 /* iOS3DRenderUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2671C2E54EFBC5C4EA504ABC /* iOS3DRenderUI */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = E5C3907C349A984CC0F909B4 /* Build configuration list for PBXNativeTarget "iOS3DRenderUI" */;
			buildPhases = (
				EE5386F7705DD7874D092AA5 /* [CP] Check Pods Manifest.lock */,
				939A35C478A994C251465463 /* Sources */,
				041AA958E5BD06A1E797DD3F /* Resources */,
				7A31935C9955C5BDB15308BB /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = iOS3DRenderUI;
			productName = iOS3DRenderUI;
			productReference = 4EB9308F7A4F21F1F0789EBF /* iOS3DRenderUI.app */;
			productType = "com.apple.product-type.application";
		};
		7902B50088C5054B8B12F61F /* iOS3DRenderUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 84FEB016A2BBACC2A0BFC224 /* Build configuration list for PBXNativeTarget "iOS3DRenderUITests" */;
			buildPhases = (
				33A1A3A35DFA26E1D8E87D12 /* [CP] Check Pods Manifest.lock */,
				B29C1A5302F91BC05930A859 /* Sources */,
				DCED519963E1EE581A50FDA7 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				1ABD9FECC8BFEB138AAFFFD8 /* PBXTargetDependency */,
			);
			name = iOS3DRenderUITests;
			productName = iOS3DRenderUITests;
			productReference = E4915DB100A5350C1B5FB1D1 /* iOS3DRenderUITests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		4BA32296A5CE703B1A159B71 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1430;
			};
			buildConfigurationList = 0608E99F6B1A93C4453DE31C /* Build configuration list for PBXProject "iOS3DRenderUI" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				Base,
				en,
			);
			mainGroup = 745CA0ED088FCE068278F0E3;
			minimizedProjectReferenceProxies = 1;
			productRefGroup = F0CCE10A537D7A12D94B1113 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2671C2E54EFBC5C4EA504ABC /* iOS3DRenderUI */,
				7902B50088C5054B8B12F61F /* iOS3DRenderUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		041AA958E5BD06A1E797DD3F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				E67EA5AAF19817B825356DEA /* SimpleMaterial.mat in Resources */,
				24D46AA52E40869500EBEEBD /* Main.storyboard in Resources */,
				24D46AAB2E41B01A00EBEEBD /* simple_textured.filamat in Resources */,
				24D46AAC2E41B01A00EBEEBD /* simple_material.mat in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		33A1A3A35DFA26E1D8E87D12 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-iOS3DRenderUITests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		EE5386F7705DD7874D092AA5 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-iOS3DRenderUI-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		939A35C478A994C251465463 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				268330EFDE4569CDC07A2685 /* AppDelegate.swift in Sources */,
				BE214CF4E22484755D1C3F31 /* FilamentRenderer.swift in Sources */,
				27541977BA817B3CED3DC3F1 /* FilamentWrapper.mm in Sources */,
				92E797756541874FDC54175A /* MainViewController.swift in Sources */,
				24D46AAE2E41B01A00EBEEBD /* FilamentMaterialManager.mm in Sources */,
				DEF15073ED4BBF4C8DE1D4D0 /* SceneDelegate.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B29C1A5302F91BC05930A859 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				68D0696DF07B20D98D3B5C26 /* iOS3DRenderUITests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		1ABD9FECC8BFEB138AAFFFD8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2671C2E54EFBC5C4EA504ABC /* iOS3DRenderUI */;
			targetProxy = 8E2A08E8050AE0F9B410CC75 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		2288C6C192CE27F5FCF0C9C0 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F4EAABDFE781F2FB9CBC2E50 /* Pods-iOS3DRenderUITests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.example.ios3drender.iOS3DRenderUITests;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOS3DRenderUI.app/iOS3DRenderUI";
			};
			name = Debug;
		};
		2BC5AB6DFCC8A73613CF4E47 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 90732D0DEC444A4912734741 /* Pods-iOS3DRenderUI.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 72RGKG7C8C;
				ENABLE_BITCODE = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				HEADER_SEARCH_PATHS = (
					"$(PODS_ROOT)/Filament/include",
					"$(PODS_ROOT)/Headers/Public",
					"$(PODS_ROOT)/Headers/Public/Filament",
				);
				INFOPLIST_FILE = iOS3DRenderUI/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(PODS_ROOT)/Filament/lib/ios",
					"$(inherited)",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.example.ios3drender.iOS3DRenderUI;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = mtec_dev_all_meitu;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "iOS3DRenderUI/iOS3DRenderUI-Bridging-Header.h";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		39A582D5455EFA995FC34912 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = C5B2513C8D044109D44A89CC /* Pods-iOS3DRenderUITests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.example.ios3drender.iOS3DRenderUITests;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/iOS3DRenderUI.app/iOS3DRenderUI";
			};
			name = Release;
		};
		5DCC0474398C0FC25541E5D1 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"DEBUG=1",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				XCODE_COMPATIBILITY_VERSION = "Xcode 14.0";
			};
			name = Debug;
		};
		CD2CAAF6C10F42758FC4406A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				SWIFT_VERSION = 5.0;
				XCODE_COMPATIBILITY_VERSION = "Xcode 14.0";
			};
			name = Release;
		};
		F1A1EBAF5034C7B77FE84406 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DA7AAA9C6C3F9CF80FA34033 /* Pods-iOS3DRenderUI.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 72RGKG7C8C;
				ENABLE_BITCODE = NO;
				GCC_C_LANGUAGE_STANDARD = c11;
				HEADER_SEARCH_PATHS = (
					"$(PODS_ROOT)/Filament/include",
					"$(PODS_ROOT)/Headers/Public",
					"$(PODS_ROOT)/Headers/Public/Filament",
				);
				INFOPLIST_FILE = iOS3DRenderUI/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(PODS_ROOT)/Filament/lib/ios",
					"$(inherited)",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.example.ios3drender.iOS3DRenderUI;
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = mtec_dev_all_meitu;
				SDKROOT = iphoneos;
				SWIFT_OBJC_BRIDGING_HEADER = "iOS3DRenderUI/iOS3DRenderUI-Bridging-Header.h";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0608E99F6B1A93C4453DE31C /* Build configuration list for PBXProject "iOS3DRenderUI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				5DCC0474398C0FC25541E5D1 /* Debug */,
				CD2CAAF6C10F42758FC4406A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		84FEB016A2BBACC2A0BFC224 /* Build configuration list for PBXNativeTarget "iOS3DRenderUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2288C6C192CE27F5FCF0C9C0 /* Debug */,
				39A582D5455EFA995FC34912 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		E5C3907C349A984CC0F909B4 /* Build configuration list for PBXNativeTarget "iOS3DRenderUI" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F1A1EBAF5034C7B77FE84406 /* Debug */,
				2BC5AB6DFCC8A73613CF4E47 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */
	};
	rootObject = 4BA32296A5CE703B1A159B71 /* Project object */;
}
