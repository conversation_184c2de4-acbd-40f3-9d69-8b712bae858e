material {
    name : "TexturedPBR",
    
    // 设置着色模型为 PBR (物理基础渲染)
    shadingModel : lit,
    
    // 启用混合和其他渲染选项
    blending : opaque,
    culling : back,
    doubleSided : false,
    
    // 定义材质参数
    parameters : [
        {
            type : sampler2d,
            name : baseColorMap
        },
        {
            type : float3,
            name : baseColorFactor,
            default : [1.0, 1.0, 1.0]
        },
        {
            type : float,
            name : metallicFactor,
            default : 0.0
        },
        {
            type : float,
            name : roughnessFactor,
            default : 0.5
        },
        {
            type : float2,
            name : textureOffset,
            default : [0.0, 0.0]
        },
        {
            type : float,
            name : textureScale,
            default : 1.0
        }
    ]
}

vertex {
    void materialVertex(inout MaterialVertexInputs material) {
        // 计算调整后的 UV 坐标
        float2 adjustedUV = material.uv0 * materialParams.textureScale + materialParams.textureOffset;
        material.uv0 = adjustedUV;
    }
}

fragment {
    void material(inout MaterialInputs material) {
        prepareMaterial(material);
        
        // 采样基础颜色纹理
        float4 baseColor = texture(materialParams_baseColorMap, getUV0()) * float4(materialParams.baseColorFactor, 1.0);
        
        // 设置材质属性
        material.baseColor = baseColor;
        material.metallic = materialParams.metallicFactor;
        material.roughness = materialParams.roughnessFactor;
    }
}